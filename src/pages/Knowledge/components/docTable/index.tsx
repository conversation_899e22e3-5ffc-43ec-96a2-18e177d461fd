import type { ColDef } from "ag-grid-community";

import noReasult from "@/assets/noResult.svg";
import TableComponent from "@/components/core/parameterRenderComponent/components/tableComponent";
import { Badge } from "@/components/main/badge";
import { <PERSON><PERSON> } from "@/components/main/button";
import { DocIcon, EditIcon } from "@/components/main/icon";
import { Input } from "@/components/main/input";
import Pagination from "@/components/main/pagination";
import { Switch } from "@/components/main/switch";
import { useCustomNavigate } from "@/customization/hooks/use-custom-navigate";
import DocStatusCell from "../docStatusCell";

type DocTableProps = {
  rowData: any[];
  current: number;
  pageSize: number;
  total: number;
  editingDoc?: string;
  setEditingDoc?: (id: string | undefined) => void;
  onDelete?: (id: string) => void;
  onPageChange?: (page: number, pageSize?: number) => void;
  onRename?: (id: string, name: string) => void;
  onRun?: (id: string) => void;
  onSwitchStatus?: (id: string, checked: boolean) => void;
};

export default function DocTable({
  rowData,
  editingDoc,
  current,
  pageSize,
  total,
  setEditingDoc,
  onDelete,
  onPageChange,
  onRename,
  onRun,
  onSwitchStatus,
}: DocTableProps) {
  const navigate = useCustomNavigate();

  const handleRename = (doc_id: string, name: string, oldName: string) => {
    setEditingDoc?.(undefined);
    if (name.trim() === "" || name === oldName) return;
    onRename?.(doc_id, name);
  };

  const openDocumentDetail = (kb_id: string, doc_id: string) => {
    navigate(`/knowledge/${kb_id}/document/${doc_id}`);
  };

  const getColumnDef = () => {
    const columns: ColDef[] = [
      {
        headerName: "序号",
        field: "index",
        width: 52,
        cellRenderer: (params) => {
          return (current - 1) * pageSize + params.node.rowIndex + 1;
        },
      },
      {
        headerName: "名称",
        field: "name",
        suppressKeyboardEvent: (params) => {
          // 防止在编辑时键盘事件被拦截
          const target = params.event.target as HTMLElement;
          const isInput =
            target?.tagName === "INPUT" || target?.tagName === "TEXTAREA";
          return isInput;
        },
        cellRenderer: (params) => {
          if (editingDoc === params.data.id) {
            return (
              <Input
                defaultValue={params.data.name}
                size="sm"
                onBlur={(e) => {
                  handleRename(
                    params.data.id,
                    e.target.value,
                    params.data.name,
                  );
                }}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    handleRename(
                      params.data.id,
                      e.currentTarget.value,
                      params.data.name,
                    );
                  }
                }}
                autoFocus
              />
            );
          }
          return (
            <div className="group inline-flex h-full w-full items-center gap-2">
              <div className="flex h-6 w-6 items-center justify-center rounded-[6px] bg-bg-primary-1">
                <DocIcon className="text-base text-primary-default" />
              </div>
              <div className="min-w-0 flex-1">
                <div
                  className="cursor-pointer truncate hover:text-primary-default"
                  onClick={() =>
                    openDocumentDetail(params.data.kb_id, params.data.id)
                  }
                >
                  {params.value}
                </div>
              </div>
              <EditIcon
                className="hidden text-base text-primary-default group-hover:block"
                onClick={() => {
                  setEditingDoc?.(params.data.id);
                }}
              />
            </div>
          );
        },
        flex: 5,
      },
      {
        headerName: "分段模式",
        field: "segmentMode",
        cellRenderer: (params) => {
          return <Badge>{params.value || "通用"}</Badge>;
        },
        flex: 2,
      },
      {
        headerName: "字节数",
        field: "size",
        flex: 2,
      },
      {
        headerName: "召回次数",
        field: "run",
        flex: 2,
      },
      {
        headerName: "上传时间",
        field: "create_date",
        flex: 4,
        valueFormatter: (params) => {
          return params.value.replace("T", " ");
        },
      },
      {
        headerName: "状态",
        field: "run",
        flex: 3,
        minWidth: 135,
        cellRenderer: (params) => {
          return <DocStatusCell record={params.data} onRun={onRun} />;
        },
      },
      {
        headerName: "向量化检索",
        field: "status",
        flex: 2,
        cellRenderer: (params) => {
          return (
            <Switch
              defaultChecked={params.value === "1"}
              onCheckedChange={(checked) =>
                onSwitchStatus?.(params.data.id, checked)
              }
            />
          );
        },
      },
      {
        headerName: "操作",
        field: "actions",
        flex: 2,
        cellRenderer: (params) => {
          return (
            <Button variant="link" onClick={() => onDelete?.(params.data.id)}>
              删除
            </Button>
          );
        },
      },
    ];

    return columns;
  };

  const noRowsTemplate = `
    <div>
      <img src=${noReasult} class="h-[120px] w-[120px]" />
      <span>搜索无结果</span>
    </div>
  `;

  return (
    <div className="flex flex-1 flex-col">
      <div className="flex-1">
        <TableComponent
          key="id"
          columnDefs={getColumnDef()}
          rowData={rowData}
          tableOptions={{ hide_options: true }}
          className="pagination-table"
          headerHeight={36}
          rowHeight={36}
          defaultColDef={{
            minWidth: 52,
            sortable: false,
            filter: false,
            resizable: false,
          }}
          enableCellTextSelection
          suppressCellFocus
          // 设置无数据时的显示模板
          overlayNoRowsTemplate={noRowsTemplate}
          // 禁用默认的无数据提示
          displayEmptyAlert={false}
          gridOptions={{
            // 保留表头
            suppressNoRowsOverlay: false,
          }}
        />
      </div>
      <div className="h-11 rounded-b-lg border border-t-0 border-border-1 px-6">
        <Pagination
          total={total}
          pageSize={pageSize}
          current={current}
          onChange={(page, pageSize) => onPageChange?.(page, pageSize)}
        />
      </div>
    </div>
  );
}
