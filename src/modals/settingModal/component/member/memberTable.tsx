import type { TableProps } from "antd";
import { Input, Space, Table, Tag } from "antd";

interface DataType {
  name: string;
  mail: string;
  department: string;
  role: string;
  status: string;
  joinTime: string;
}

const columns: TableProps<DataType>["columns"] = [
  {
    title: "姓名",
    dataIndex: "name",
    key: "name",
    render: (text) => <a>{text}</a>,
  },
  {
    title: "邮箱",
    dataIndex: "mail",
    key: "mail",
  },
  {
    title: "部门",
    dataIndex: "department",
    key: "department",
  },
  {
    title: "角色",
    key: "role",
    dataIndex: "role",
  },
  {
    title: "状态",
    key: "status",
    dataIndex: "status",
  },
  {
    title: "加入时间",
    key: "joinTime",
    dataIndex: "joinTime",
  },
  {
    title: "操作",
    key: "action",
    render: (_, record) => (
      <Space size="middle">
        <a>编辑</a>
        <a>改密</a>
        <a>删除</a>
      </Space>
    ),
  },
];

const data: DataType[] = [
  {
    name: "张三",
    mail: "zhang<PERSON>@zepmemory.com",
    department: "技术部",
    role: "管理员",
    status: "正常",
    joinTime: "2023-01-01",
  },
  {
    name: "李四",
    mail: "<EMAIL>",
    department: "技术部",
    role: "普通用户",
    status: "正常",
    joinTime: "2023-01-01",
  },
  {
    name: "王五",
    mail: "<EMAIL>",
    department: "技术部",
    role: "普通用户",
    status: "正常",
    joinTime: "2023-01-01",
  },
];

function MemberTable() {
  return (
    <div>
      <Input className="mb-2" />
      <Table<DataType> columns={columns} dataSource={data} size="middle" />
    </div>
  );
}

export default MemberTable;
