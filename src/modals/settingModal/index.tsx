import { Button } from "@/components/main/button";
import { CircleOutlineCloseIcon, UserInfoIcon } from "@/components/main/icon";
import Modal from "@/components/main/modal";
import { useLogout } from "@/controllers/API/queries/auth";
import { cn } from "@/utils/utils";
import { useState } from "react";
import Member from "./component/member";
import UserContent from "./component/userContent";

type SettingModalProps = {
  open: boolean;
  setOpen: (open: boolean) => void;
};

const menus = [
  {
    title: "账户信息",
    key: "account",
    icon: <UserInfoIcon />,
  },
  {
    title: "成员管理",
    key: "member",
    icon: <UserInfoIcon />,
  },
];

export default function SettingModal({ open, setOpen }: SettingModalProps) {
  const [curTab, setCurTab] = useState("account");

  const { mutate: mutationLogout } = useLogout();

  const handleLogout = () => {
    mutationLogout();
  };

  const renderContent = (key: string) => {
    switch (key) {
      case "account": {
        return <UserContent />;
      }
      case "member": {
        return <Member />;
      }
      default: {
        return null;
      }
    }
  };

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      className="h-[700px] w-[1200px] bg-bg-light-1"
    >
      <Modal.Content className="flex h-full flex-row bg-bg-light-1 p-0 shadow-toastbg">
        <div
          className="fixed left-[calc(50%-680px)] top-[calc(50%-370px)] flex h-[60px] w-[60px] cursor-pointer flex-col items-center gap-1 rounded-xl border border-text-2-icon p-2"
          onClick={() => setOpen(false)}
        >
          <div className="w-full text-start">
            <CircleOutlineCloseIcon className="text-2xl text-text-2-icon" />
          </div>
          <div className="w-full text-start text-xs">ESC</div>
        </div>

        <div className="flex h-full w-[200px] flex-col gap-4 border-r border-border-1 bg-bg-light-3 px-6 py-4">
          <div className="text-base font-medium text-text-1">设置</div>
          <div className="flex h-[588px] flex-col gap-2 font-normal">
            {menus.map((item) => {
              return (
                <div
                  className={cn(
                    "flex w-full cursor-pointer items-center gap-1 rounded-lg px-2 py-[5px] text-sm text-text-3 hover:bg-bg-light-1",
                    curTab === item.key &&
                      "bg-bg-light-5 font-medium text-text-1",
                  )}
                  key={item.key}
                  onClick={() => setCurTab(item.key)}
                >
                  <span className="text-[20px]">{item.icon}</span>
                  <span className="text-sm text-text-1">{item.title}</span>
                </div>
              );
            })}
          </div>
          <Button variant="destructive" onClick={handleLogout}>
            退出登录
          </Button>
        </div>
        <div className="flex-1">{renderContent(curTab)}</div>
      </Modal.Content>
    </Modal>
  );
}
